@php
    $variant = $attributes['variant'] ?? 'default';
@endphp

@if($variant === 'line')
    <div class="internship-benefits-section internship-benefits-line flex justify-center items-center">
        <div class="container lg:max-w-[856px] lg:m-auto px-5 py-8 lg:py-[62px]">
            <div class="flex flex-col lg:flex-row lg:gap-16">
                <div class="lg:w-1/2 mb-8 lg:mb-0">
                    @if(!empty($attributes['title']))
                        <x-title variation="large" class="mb-4">{!! $attributes['title'] !!}</x-title>
                    @endif
                    @if(!empty($attributes['description']))
                        <p class="lg:max-w-[470px]">{!! $attributes['description'] !!}</p>
                    @endif
                </div>

                @if(!empty($attributes['benefits']) && count($attributes['benefits']) > 0)
                    <div class="lg:w-1/2 flex flex-col lg:max-w-[416px]" data-benefits-container>
                        @foreach($attributes['benefits'] as $index => $benefit)
                            <div class="benefit-line-item cursor-pointer mb-8 last:mb-0" data-benefit-index="{{ $index }}">
                                <div class="flex gap-4 items-start">
                                    <div class="icon-container relative flex flex-col items-center">
                                        <div class="rounded-[8px] mb-3 w-9 h-9" style="background: linear-gradient(54.97deg, #CCEFFF -1.61%, #CCEFFF 53.98%, #D7F4E1 100%);padding: 3px;z-index: 1;">
                                            <div class="w-full h-full bg-white/80 rounded-md flex items-center justify-center">
                                                @if(!empty($benefit['icon']))
                                                    <img src="{{ $benefit['icon'] }}" alt="" class="w-6 h-6 object-contain">
                                                @endif
                                            </div>
                                        </div>
                                        {{-- Vertical progress line --}}
                                        @if($index < count($attributes['benefits']) - 1)
                                            <div class="progress-line rounded-3xl w-1 h-full bg-[#F4F6FA] overflow-hidden relative"
                                                 data-line-index="{{ $index }}"
                                                 style="min-height:57px;">
                                                <div class="progress-fill rounded-3xl w-full bg-[#CCEFFF] absolute top-0 left-0" style="height:0;"></div>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="benefit-content flex-1">
                                        @if(!empty($benefit['title']))
                                            <span class="block text-bodyMedium font-normal lg:text-bodyMediumDesktop mb-4">{!! $benefit['title'] !!}</span>
                                        @endif
                                        <div class="benefit-description overflow-hidden transition-all duration-700 ease-in-out"
                                             data-benefit-content="{{ $index }}"
                                             style="max-height: {{ $index === 0 ? '200px' : '0' }}; opacity: {{ $index === 0 ? '1' : '0' }};">
                                            @if(!empty($benefit['description']))
                                                <div class="description-inner pb-2">
                                                    <p class="text-bodySmall lg:text-bodySmallDesktop text-[#566276]">{!! $benefit['description'] !!}</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
@else
    <div class="internship-benefits-section flex justify-center items-center">
        <div class="container px-5 py-8 lg:py-[62px]">
            <div class="flex flex-col lg:flex-row lg:gap-16">
                <div class="lg:w-1/2 mb-8 lg:mb-0">
                    @if(!empty($attributes['title']))
                        <x-title variation="large" class="mb-4">{!! $attributes['title'] !!}</x-title>
                    @endif
                    @if(!empty($attributes['description']))
                        <p class="lg:max-w-[470px]">{!! $attributes['description'] !!}</p>
                    @endif
                </div>

                @if(!empty($attributes['benefits']) && count($attributes['benefits']) > 0)
                    <div class="lg:w-1/2 flex flex-col gap-3 lg:max-w-[416px]">
                        @foreach($attributes['benefits'] as $benefit)
                            <div class="benefit-item border border-solid border-[#E8EBF3] rounded-[20px] p-4 flex gap-4">
                                <div class="icon-container relative w-9 h-9 flex-shrink-0">
                                    <div class="absolute inset-0 rounded-[8px]" style="
                                        background: linear-gradient(54.97deg, #CCEFFF -1.61%, #CCEFFF 53.98%, #D7F4E1 100%);
                                        padding: 3px;
                                        z-index: 1;
                                    ">
                                        <div class="w-full h-full bg-white/80 rounded-md flex items-center justify-center">
                                            @if(!empty($benefit['icon']))
                                                <img src="{{ $benefit['icon'] }}" alt="" class="w-6 h-6 object-contain">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="benefit-content">
                                    @if(!empty($benefit['title']))
                                        <span class="block text-bodyMedium font-normal lg:text-bodyMediumDesktop">{!! $benefit['title'] !!}</span>
                                    @endif
                                    @if(!empty($benefit['description']))
                                        <p class="text-bodySmall lg:text-bodySmallDesktop text-[#566276]">{!! $benefit['description'] !!}</p>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
@endif
