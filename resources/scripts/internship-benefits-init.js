document.addEventListener('DOMContentLoaded', function() {
    const benefitsSections = document.querySelectorAll('.internship-benefits-line');

    benefitsSections.forEach(section => {
        const benefitsContainer = section.querySelector('[data-benefits-container]');
        if (!benefitsContainer) return;

        const benefitItems = benefitsContainer.querySelectorAll('.benefit-line-item');
        const progressLines = benefitsContainer.querySelectorAll('.progress-line');
        const benefitContents = benefitsContainer.querySelectorAll('[data-benefit-content]');

        if (benefitItems.length === 0) return;

        let currentActiveIndex = 0;
        let progressTimeout = null;
        let progressInterval = null;

        // Calculate content height for proper animation
        function calculateContentHeight(content) {
            // Find the actual paragraph element inside the content
            const innerParagraph = content.querySelector('p');
            if (!innerParagraph) return 200; // fallback height

            // Create a clone of the inner content div (description-inner)
            const descriptionInner = content.querySelector('.description-inner');
            if (!descriptionInner) return 200; // fallback height

            const clone = descriptionInner.cloneNode(true);
            clone.style.position = 'absolute';
            clone.style.visibility = 'hidden';
            clone.style.top = '-9999px';
            clone.style.width = content.offsetWidth + 'px'; // Match the original width
            clone.style.maxHeight = 'none';
            clone.style.height = 'auto';

            content.parentNode.appendChild(clone);
            const height = clone.offsetHeight;
            content.parentNode.removeChild(clone);

            // Add some padding to ensure we don't cut off content
            return height + 10;
        }

        // Set initial progress line heights (all items start closed except first)
        function setInitialProgressLineHeights() {
            console.log('=== Setting initial progress line heights ===');
            console.log(`Found ${benefitItems.length} benefit items and ${progressLines.length} progress lines`);

            benefitItems.forEach((_, index) => {
                if (index < progressLines.length) {
                    // Add smooth transitions to all progress lines
                    progressLines[index].style.transition = 'height 700ms ease-in-out';

                    // For initial setup, calculate based on closed state (57px minimum)
                    // except for the first item which starts open
                    if (index === 0) {
                        console.log(`Setting initial height for first item (${index})`);
                        // For the first item, we'll calculate after content is set up
                        progressLines[index].style.height = '57px'; // Start with minimum, will be updated
                    } else {
                        console.log(`Setting initial height for item ${index} to 57px (closed state)`);
                        progressLines[index].style.height = '57px';
                    }
                }
            });
        }

        // Initialize: hide all content except first
        benefitContents.forEach((content, index) => {
            if (index === 0) {
                // First item starts open with calculated height
                const targetHeight = calculateContentHeight(content);
                content.style.maxHeight = targetHeight + 'px';
                content.style.opacity = '1';
            } else {
                // All other items start closed
                content.style.maxHeight = '0';
                content.style.opacity = '0';
            }
        });

        // Set initial progress line heights
        setInitialProgressLineHeights();

        // Initialize all progress fills to height 0
        progressLines.forEach(line => {
            const progressFill = line.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.height = '0';
            }
        });

        // Reset all progress lines
        function resetAllProgress() {
            progressLines.forEach(line => {
                const progressFill = line.querySelector('.progress-fill');
                if (progressFill) {
                    progressFill.style.height = '0';
                    progressFill.style.transitionDuration = '0s';
                }
            });
        }

        // Calculate progress line height: item height - 48px, minimum 57px
        function calculateProgressLineHeight(itemIndex) {
            const benefitItem = benefitItems[itemIndex];
            const itemHeight = benefitItem.offsetHeight;
            const lineHeight = Math.max(57, itemHeight - 48);

            console.log(`Item ${itemIndex}: height=${itemHeight}px, line=${lineHeight}px`);
            return lineHeight;
        }

        // Show content for specific item with fade animation
        function showContent(index) {
            console.log(`=== Showing content for item ${index} ===`);

            // First, add smooth transitions to all progress lines
            progressLines.forEach(line => {
                line.style.transition = 'height 700ms ease-in-out';
            });

            benefitContents.forEach((content, i) => {
                if (i === index) {
                    // Calculate the actual content height needed
                    const targetContentHeight = calculateContentHeight(content);
                    console.log(`Content ${i} expanding to ${targetContentHeight}px`);

                    // Fade in and expand the active content
                    content.style.opacity = '1';
                    content.style.maxHeight = targetContentHeight + 'px';
                } else {
                    console.log(`Content ${i} collapsing to 0px`);

                    // Reset progress line to minimum height for inactive items
                    if (i < progressLines.length) {
                        console.log(`Progress line ${i} collapsing to 57px`);
                        progressLines[i].style.height = '57px';
                    }

                    // Fade out and collapse inactive content
                    content.style.opacity = '0';
                    content.style.maxHeight = '0';
                }
            });

            // Wait for content to expand, then measure and set progress line height
            setTimeout(() => {
                if (index < progressLines.length) {
                    const targetLineHeight = calculateProgressLineHeight(index);
                    console.log(`Setting progress line ${index} to ${targetLineHeight}px after expansion`);
                    progressLines[index].style.height = targetLineHeight + 'px';
                }
            }, 750); // Wait for content expansion to complete
        }

        // Start progress animation for specific line
        function startProgress(index) {
            if (index >= progressLines.length) return;

            const progressLine = progressLines[index];
            const progressFill = progressLine.querySelector('.progress-fill');

            if (!progressFill) return;

            // Reset and start animation
            progressFill.style.transitionDuration = '0s';
            progressFill.style.height = '0';

            // Force reflow
            progressFill.offsetHeight;

            // Start animation - animate to 100% height
            progressFill.style.transitionDuration = '4s';
            progressFill.style.transitionTimingFunction = 'linear';
            progressFill.style.height = '100%';
        }

        // Stop all progress and timeouts
        function stopAllProgress() {
            if (progressTimeout) {
                clearTimeout(progressTimeout);
                progressTimeout = null;
            }
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
            resetAllProgress();
        }

        // Activate specific item
        function activateItem(index) {
            stopAllProgress();
            currentActiveIndex = index;
            showContent(index);

            // Start progress for current item if it has a progress line
            // Wait for the progress line height to be set before starting animation
            if (index < progressLines.length) {
                setTimeout(() => {
                    startProgress(index);
                }, 800); // Wait slightly longer than the content expansion (750ms)

                // Set timeout to move to next item (total time: 800ms delay + 4000ms animation)
                progressTimeout = setTimeout(() => {
                    const nextIndex = (index + 1) % benefitItems.length;
                    activateItem(nextIndex);
                }, 4800);
            }
        }

        // Add click handlers to benefit items
        benefitItems.forEach((item, index) => {
            item.addEventListener('click', function() {
                // If clicking the already active item, restart its progress
                if (index === currentActiveIndex) {
                    activateItem(index);
                } else {
                    // Activate the clicked item
                    activateItem(index);
                }
            });
        });

        // Calculate initial progress line height for the first item after content is set up
        setTimeout(() => {
            if (progressLines.length > 0) {
                const initialLineHeight = calculateProgressLineHeight(0);
                console.log(`Setting initial progress line 0 height to ${initialLineHeight}px`);
                progressLines[0].style.height = initialLineHeight + 'px';
            }
        }, 100);

        // Start the automatic progression with the first item
        activateItem(0);
    });
});