<?php
/**
 * EnlighterJS Syntax Highlighter Integration
 *
 * Loads EnlighterJS via CDN instead of NPM package due to build compatibility issues.
 * EnlighterJS is not designed as an ES6 module and doesn't integrate well with modern
 * webpack-based build systems like Bud.js. CDN approach ensures reliable loading.
 *
 * INTEGRATION APPROACH:
 * - CDN delivery via jsDelivr for consistent availability
 * - Avoids webpack module resolution and build process complications
 * - Provides syntax highlighting for code blocks across the site
 * - Version pinned to 3.4.0 for stability
 *
 * ALTERNATIVE APPROACHES CONSIDERED:
 * - NPM package: Incompatible with ES6 module system and Bud.js build process
 * - WordPress plugin: Avoided to maintain theme-level control and reduce plugin dependencies
 * - Local files: CDN provides better caching and reduces theme bundle size
 */

add_action('wp_enqueue_scripts', function () {
    wp_enqueue_style(
        'enlighterjs-style',
        'https://cdn.jsdelivr.net/npm/enlighterjs@3.4.0/dist/enlighterjs.min.css',
        [],
        '3.4.0'
    );

    wp_enqueue_script(
        'enlighterjs-script',
        'https://cdn.jsdelivr.net/npm/enlighterjs@3.4.0/dist/enlighterjs.min.js',
        [],
        '3.4.0',
        true
    );
});