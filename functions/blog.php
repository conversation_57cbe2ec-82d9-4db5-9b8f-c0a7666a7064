<?php
/**
 * Blog AJAX Functionality
 *
 * This file provides the server-side AJAX handler for the blog's dynamic post loading system.
 * It enables category filtering and pagination without page reloads, creating a smooth user
 * experience for browsing blog content with real-time filtering and load-more functionality.
 *
 * RELATED FILES AND INTEGRATION:
 * - Frontend Script: resources/scripts/blog-init.js (handles AJAX requests, category filtering, pagination)
 * - Blog Template: resources/views/sections/blog.blade.php (initial blog page layout and data attributes)
 * - Post Item Template: resources/views/partials/blog-post-item.blade.php (individual post card template)
 * - Blog Block: resources/scripts/blocks/blog.js (<PERSON><PERSON>nberg block for blog configuration)
 * - Single Post: resources/views/partials/content-single.blade.php (individual blog post display)
 *
 * SYSTEM ARCHITECTURE:
 * 1. Initial Load: Blog template renders first 6 posts with category pills and pagination data
 * 2. Category Filtering: JavaScript sends AJAX request with selected category to filter posts
 * 3. Load More: JavaScript requests next page of posts and appends to existing grid
 * 4. Template Rendering: Server uses same blog-post-item template for consistent styling
 * 5. Response Data: Returns HTML content plus pagination metadata for frontend state management
 *
 * AJAX ENDPOINT: /wp-admin/admin-ajax.php
 * - Action: 'load_more_posts'
 * - Available to: Both logged-in and non-logged-in users (wp_ajax and wp_ajax_nopriv)
 * - Method: POST
 *
 * REQUEST PARAMETERS:
 * - category: Category slug for filtering ('all' for no filter)
 * - page: Page number for pagination (1-based)
 * - posts_per_page: Number of posts to load (default: 6)
 *
 * RESPONSE DATA:
 * - posts_html: Rendered HTML for post cards using blog-post-item template
 * - max_pages: Total number of pages available for current filter
 * - found_posts: Total number of posts matching current filter
 * - posts_returned: Number of posts in current response
 *
 * FRONTEND FEATURES:
 * - Category Pills: Dynamic filtering with visual active states
 * - Load More Button: Shows remaining post count and loads next batch
 * - Swiper Integration: Popular articles carousel in blog hero section
 * - Responsive Design: Grid layout adapts to screen size
 * - Hover Effects: Post cards scale on hover for better UX
 *
 */

add_action('wp_ajax_load_more_posts', 'load_more_posts_ajax_handler');
add_action('wp_ajax_nopriv_load_more_posts', 'load_more_posts_ajax_handler');

function load_more_posts_ajax_handler() {
    $category       = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : 'all';
    $page           = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $posts_per_page = isset($_POST['posts_per_page']) ? intval($_POST['posts_per_page']) : 6;

    $args = array(
        'post_status'    => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged'          => $page,
    );

    if ($category !== 'all') {
        $args['category_name'] = $category;
    }

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();
            $post_id         = get_the_ID();
            $post_categories = get_the_category($post_id);
            $category_slugs  = $post_categories ? wp_list_pluck($post_categories, 'slug') : [];
            $author_id       = get_the_author_meta('ID');
            $author_name     = get_the_author();
            $author_avatar   = get_avatar_url($author_id);
            $permalink       = get_permalink($post_id);

            echo \Roots\view('partials.blog-post-item', [
                'permalink'      => $permalink,
                'post_id'        => $post_id,
                'date'           => get_the_date('d.m.Y', $post_id),
                'has_thumbnail'  => has_post_thumbnail($post_id),
                'thumbnail_url'  => get_the_post_thumbnail_url($post_id, 'full'),
                'title'          => get_the_title($post_id),
                'excerpt'        => wp_trim_words(get_the_excerpt(), 20),
                'author_avatar'  => $author_avatar,
                'author_name'    => $author_name,
                'categories'     => $post_categories,
                'category_slugs' => $category_slugs,
            ])->render();
        }
        wp_reset_postdata();
        $posts_html     = ob_get_clean();
        $max_pages      = $query->max_num_pages;
        $found_posts    = $query->found_posts;
        $posts_returned = $query->post_count;

        wp_send_json_success(array(
            'posts_html'     => $posts_html,
            'max_pages'      => $max_pages,
            'found_posts'    => $found_posts,
            'posts_returned' => $posts_returned,
        ));
    } else {
        wp_send_json_error('No posts found');
    }
}
