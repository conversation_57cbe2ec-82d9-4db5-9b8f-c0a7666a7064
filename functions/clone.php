<?php
/**
 * Case Study Cloning Functionality
 *
 * Adds "Clone" action to case_study post type in admin. Custom cloning functionality was added
 * because Yoast SEO has cloning features but not for custom post types, so this fills that gap.
 *
 * RELATED FILES:
 * - cpt/case_studies.php (case_study post type registration)
 * - resources/scripts/blocks/case-study-*.js (various case study Gutenberg blocks)
 *
 * FEATURES:
 * - Adds "Clone" link to case study post row actions
 * - Copies all post content, taxonomies (industries/services), and meta fields
 * - Fixes HTML encoding issues in Gutenberg block content
 * - Creates clones as drafts with "(Clone)" title suffix
 * - Redirects to edit screen after cloning
 */

function my_duplicate_post_link( $actions, $post ) {
    if ( 'case_study' === $post->post_type && current_user_can( 'edit_posts' ) ) {
        $url = admin_url( 'admin.php?action=my_duplicate_post&post=' . $post->ID );
        $actions['duplicate'] = '<a href="' . esc_url( $url ) . '" title="Clone this post" rel="permalink">Clone</a>';
    }
    return $actions;
}
add_filter( 'post_row_actions', 'my_duplicate_post_link', 10, 2 );
add_filter( 'page_row_actions', 'my_duplicate_post_link', 10, 2 );

function my_duplicate_post_action() {
    if ( empty( $_GET['post'] ) ) {
        wp_die( 'No post to duplicate provided!' );
    }
    $post_id = absint( $_GET['post'] );
    $post    = get_post( $post_id );
    if ( ! $post || 'case_study' !== $post->post_type ) {
        wp_die( 'Post not found or invalid post type.' );
    }
    if ( ! current_user_can( 'edit_posts', $post_id ) ) {
        wp_die( 'Permission denied.' );
    }

    $raw_content   = wp_unslash( $post->post_content );
    $fixed_content = serialize_blocks( parse_blocks( $raw_content ) );

    $fixed_content = preg_replace_callback(
        '/(u003cbru003e)+/',
        function( $matches ) {
            $occurrences = strlen( $matches[0] ) / strlen( 'u003cbru003e' );
            return str_repeat( '<br>', $occurrences );
        },
        $fixed_content
    );

    $fixed_content = str_replace( array( 'u003c', 'u003e' ), array( '<', '>' ), $fixed_content );

    $new_post = array(
        'post_title'   => $post->post_title . ' (Clone)',
        'post_content' => $fixed_content,
        'post_excerpt' => $post->post_excerpt,
        'post_status'  => 'draft',
        'post_type'    => $post->post_type,
        'post_author'  => get_current_user_id(),
    );
    $new_post_id = wp_insert_post( $new_post );
    if ( is_wp_error( $new_post_id ) ) {
        wp_die( 'Error duplicating post.' );
    }

    $taxonomies = get_object_taxonomies( $post->post_type );
    foreach ( $taxonomies as $taxonomy ) {
        $terms = wp_get_object_terms( $post_id, $taxonomy, array( 'fields' => 'slugs' ) );
        wp_set_object_terms( $new_post_id, $terms, $taxonomy, false );
    }

    $meta_data = get_post_meta( $post_id );
    if ( $meta_data ) {
        foreach ( $meta_data as $meta_key => $meta_values ) {
            if ( '_wp_old_slug' === $meta_key ) {
                continue;
            }
            foreach ( $meta_values as $meta_value ) {
                add_post_meta( $new_post_id, $meta_key, maybe_unserialize( $meta_value ) );
            }
        }
    }

    wp_redirect( admin_url( 'post.php?action=edit&post=' . $new_post_id ) );
    exit;
}
add_action( 'admin_action_my_duplicate_post', 'my_duplicate_post_action' );
