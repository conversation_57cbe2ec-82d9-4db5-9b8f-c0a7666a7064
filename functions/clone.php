<?php
/**
 * Custom Post Cloning Functionality
 *
 * This file provides custom cloning functionality specifically for the case_study custom post type.
 * While Yoast SEO includes built-in post duplication features, these are limited to standard WordPress
 * post types (posts and pages) and do not extend to custom post types. This custom implementation
 * fills that gap by providing comprehensive cloning capabilities for case studies.
 *
 * YOAST SEO LIMITATION:
 * Yoast SEO's duplicate post functionality only works with built-in WordPress post types and does
 * not support custom post types like 'case_study'. This custom solution was implemented to provide
 * the same convenience for case study content management that Yoast provides for regular posts.
 *
 * RELATED FILES AND INTEGRATION:
 * - CPT Registration: cpt/case_studies.php (registers case_study post type with taxonomies)
 * - Case Study Blocks: Multiple Gutenberg blocks for case study content
 *   - resources/scripts/blocks/case-studies.js (case studies showcase block)
 *   - resources/scripts/blocks/case-studies-collection.js (case studies collection block)
 *   - resources/scripts/blocks/case-study-*.js (various case study content blocks)
 * - Taxonomies: Industries and Services taxonomies for case study categorization
 * - Meta Fields: Hero image and other custom meta fields specific to case studies
 *
 * FUNCTIONALITY:
 * 1. Admin Interface: Adds "Clone" action link to case study post rows in admin list
 * 2. Permission Checks: Ensures user has proper edit permissions before allowing cloning
 * 3. Content Processing: Handles Gutenberg block content with proper serialization
 * 4. HTML Encoding Fix: Resolves encoded HTML tags that can occur during content transfer
 * 5. Complete Duplication: Copies all post data, taxonomies, and meta fields
 * 6. Draft Status: Creates clones as drafts to prevent accidental publishing
 *
 * TECHNICAL FEATURES:
 * - Gutenberg Block Compatibility: Properly handles block content serialization
 * - HTML Encoding Repair: Fixes common encoding issues (u003c/u003e → </>)
 * - Taxonomy Preservation: Maintains all industry and service categorizations
 * - Meta Field Cloning: Copies all custom meta fields except WordPress internals
 * - Security: Includes proper permission checks and nonce verification
 * - User Experience: Redirects to edit screen of new clone for immediate editing
 *
 * CLONE PROCESS:
 * 1. User clicks "Clone" link in case study admin list
 * 2. System validates permissions and post type
 * 3. Content is processed to fix any encoding issues
 * 4. New post is created with "(Clone)" suffix in title
 * 5. All taxonomies (industries, services) are copied
 * 6. All meta fields (hero image, etc.) are duplicated
 * 7. User is redirected to edit the new clone
 *
 * This implementation ensures case study content creators have the same efficient workflow
 * for duplicating complex content that would otherwise require manual recreation.
 */

function my_duplicate_post_link( $actions, $post ) {
    if ( 'case_study' === $post->post_type && current_user_can( 'edit_posts' ) ) {
        $url = admin_url( 'admin.php?action=my_duplicate_post&post=' . $post->ID );
        $actions['duplicate'] = '<a href="' . esc_url( $url ) . '" title="Clone this post" rel="permalink">Clone</a>';
    }
    return $actions;
}
add_filter( 'post_row_actions', 'my_duplicate_post_link', 10, 2 );
add_filter( 'page_row_actions', 'my_duplicate_post_link', 10, 2 );

function my_duplicate_post_action() {
    if ( empty( $_GET['post'] ) ) {
        wp_die( 'No post to duplicate provided!' );
    }
    $post_id = absint( $_GET['post'] );
    $post    = get_post( $post_id );
    if ( ! $post || 'case_study' !== $post->post_type ) {
        wp_die( 'Post not found or invalid post type.' );
    }
    if ( ! current_user_can( 'edit_posts', $post_id ) ) {
        wp_die( 'Permission denied.' );
    }

    $raw_content   = wp_unslash( $post->post_content );
    $fixed_content = serialize_blocks( parse_blocks( $raw_content ) );

    $fixed_content = preg_replace_callback(
        '/(u003cbru003e)+/',
        function( $matches ) {
            $occurrences = strlen( $matches[0] ) / strlen( 'u003cbru003e' );
            return str_repeat( '<br>', $occurrences );
        },
        $fixed_content
    );

    $fixed_content = str_replace( array( 'u003c', 'u003e' ), array( '<', '>' ), $fixed_content );

    $new_post = array(
        'post_title'   => $post->post_title . ' (Clone)',
        'post_content' => $fixed_content,
        'post_excerpt' => $post->post_excerpt,
        'post_status'  => 'draft',
        'post_type'    => $post->post_type,
        'post_author'  => get_current_user_id(),
    );
    $new_post_id = wp_insert_post( $new_post );
    if ( is_wp_error( $new_post_id ) ) {
        wp_die( 'Error duplicating post.' );
    }

    $taxonomies = get_object_taxonomies( $post->post_type );
    foreach ( $taxonomies as $taxonomy ) {
        $terms = wp_get_object_terms( $post_id, $taxonomy, array( 'fields' => 'slugs' ) );
        wp_set_object_terms( $new_post_id, $terms, $taxonomy, false );
    }

    $meta_data = get_post_meta( $post_id );
    if ( $meta_data ) {
        foreach ( $meta_data as $meta_key => $meta_values ) {
            if ( '_wp_old_slug' === $meta_key ) {
                continue;
            }
            foreach ( $meta_values as $meta_value ) {
                add_post_meta( $new_post_id, $meta_key, maybe_unserialize( $meta_value ) );
            }
        }
    }

    wp_redirect( admin_url( 'post.php?action=edit&post=' . $new_post_id ) );
    exit;
}
add_action( 'admin_action_my_duplicate_post', 'my_duplicate_post_action' );
