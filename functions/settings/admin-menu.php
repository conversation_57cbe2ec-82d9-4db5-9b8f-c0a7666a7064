<?php
/**
 * Admin Menu Setup
 *
 * This file creates the WordPress admin menu structure for all theme settings pages.
 * It establishes a centralized "Theme Settings" menu with organized submenus for different
 * functionality areas including content management, asset repositories, and optimization tools.
 *
 * RELATED FILES AND INTEGRATION:
 * - Entry Point: functions/settings/index.php (includes this file)
 * - Settings Pages: All files in functions/settings/pages/ directory
 *   - contact-forms.php: Provides contact_forms_page() function
 *   - technology-icons.php: Provides technology_icons_page() function
 *   - positions.php: Provides positions_page() function
 *   - case-study-icons.php: Provides case_study_icons_page() function
 *   - image-optimization.php: Provides image_optimization_page() function
 * - CPT Integration: functions/settings/cpt-registration.php (registers contact_form post type)
 * - Form Processing: functions/form.php (handles contact form submissions)
 *
 * MENU STRUCTURE:
 * - Main Menu: "Theme Settings" (position 60, dashicons-admin-generic)
 * - Contact Forms: Manages blog post contact forms and default submission email
 * - Technology Icons: Central repository for technology/framework icons used in blocks
 * - Positions: Repository for position/role icons used in Case Study Timeline blocks
 * - Case Study Icons: Repository for case study icons with accent colors
 * - Image Optimization: WebP/AVIF conversion settings and bulk regeneration tools
 *
 * All submenus require 'manage_options' capability and link to their respective page functions
 * defined in the settings pages directory. The main menu item uses '__return_false' as it
 * serves only as a parent container for the submenus.
 */

add_action('admin_menu', function() {
    add_menu_page(
        'Theme Settings',
        'Theme Settings',
        'manage_options',
        'theme-settings',
        '__return_false',
        'dashicons-admin-generic',
        60
    );
    add_submenu_page(
        'theme-settings',
        'Contact Forms',
        'Contact Forms',
        'manage_options',
        'theme-settings-contact-forms',
        'contact_forms_page'
    );
    add_submenu_page(
        'theme-settings',
        'Technology Icons',
        'Technology Icons',
        'manage_options',
        'theme-settings-technology-icons',
        'technology_icons_page'
    );
    add_submenu_page(
        'theme-settings',
        'Positions',
        'Positions',
        'manage_options',
        'theme-settings-positions',
        'positions_page'
    );
    add_submenu_page(
        'theme-settings',
        'Case Study Icons',
        'Case Study Icons',
        'manage_options',
        'theme-settings-case-study-icons',
        'case_study_icons_page'
    );
    add_submenu_page(
        'theme-settings',
        'Image Optimization',
        'Image Optimization',
        'manage_options',
        'theme-settings-image-optimization',
        'image_optimization_page'
    );
});
