<?php
/**
 * Image Optimization Settings Page
 *
 * This file provides the WordPress admin interface for configuring automatic image optimization settings.
 * It handles conversion of JPG, PNG, and GIF images to modern WebP and AVIF formats with configurable
 * quality settings. The system includes:
 *
 * - Toggle switches for enabling/disabling optimization features
 * - Quality sliders for WebP (recommended 80-90) and AVIF (recommended 70-80) formats
 * - Comparison mode toggle to serve original images for A/B testing
 * - Bulk regeneration tool for existing media library images
 * - Smart regeneration that only updates images when quality settings change
 * - Processing of all WordPress image sizes (thumbnails, medium, large, etc.)
 * - Error handling and detailed statistics reporting
 *
 * The optimization process automatically converts uploaded images and serves the most efficient
 * format supported by the user's browser, falling back to original formats when needed.
 */

function atlantbh_regenerate_webp_avif($only_changed_quality = false) {
    global $atlantbh_image_optimizer;

    $stats = [
        'processed' => 0,
        'webp_success' => 0,
        'avif_success' => 0,
        'skipped' => 0,
        'quality_unchanged' => 0,
        'errors' => 0
    ];

    $args = [
        'post_type' => 'attachment',
        'post_mime_type' => ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'],
        'post_status' => 'inherit',
        'posts_per_page' => -1,
    ];

    $images = get_posts($args);
    $errors = [];

    foreach ($images as $image) {
        try {
            $stats['processed']++;

            $file_path = get_attached_file($image->ID);

            if (!$file_path || !file_exists($file_path)) {
                $stats['skipped']++;
                continue;
            }

            $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
            if ($ext === 'svg') {
                $stats['skipped']++;
                continue;
            }

            if (!in_array($ext, ['jpg', 'jpeg', 'png', 'gif'])) {
                $stats['skipped']++;
                continue;
            }

            $settings = $atlantbh_image_optimizer->get_settings();
            $current_webp_quality = $settings['webp_quality'];
            $current_avif_quality = $settings['avif_quality'];

            $webp_quality_info = get_option('atlantbh_webp_quality_info', []);
            $avif_quality_info = get_option('atlantbh_avif_quality_info', []);
            $file_key = md5($file_path);

            $stored_webp_quality = isset($webp_quality_info[$file_key]) ? $webp_quality_info[$file_key] : null;
            $stored_avif_quality = isset($avif_quality_info[$file_key]) ? $avif_quality_info[$file_key] : null;

            $webp_exists = $stored_webp_quality !== null;
            $avif_exists = $stored_avif_quality !== null;

            $need_webp_regen = !$only_changed_quality ||
                               ($webp_exists && $stored_webp_quality !== $current_webp_quality);
            $need_avif_regen = !$only_changed_quality ||
                               ($avif_exists && $stored_avif_quality !== $current_avif_quality);

            if ($need_webp_regen && $settings['enable_webp']) {
                $webp_result = $atlantbh_image_optimizer->convert_to_webp($file_path, true);
                if ($webp_result) {
                    $stats['webp_success']++;
                }
            } else if ($settings['enable_webp']) {
                $stats['quality_unchanged']++;
            }

            if ($need_avif_regen && $settings['enable_avif']) {
                $avif_result = $atlantbh_image_optimizer->convert_to_avif($file_path, true);
                if ($avif_result) {
                    $stats['avif_success']++;
                }
            } else if ($settings['enable_avif']) {
                $stats['quality_unchanged']++;
            }

            $metadata = wp_get_attachment_metadata($image->ID);

            if (!isset($metadata['sizes']) || !is_array($metadata['sizes'])) {
                continue;
            }

            $upload_dir = wp_upload_dir();
            $base_dir = dirname($file_path);

            foreach ($metadata['sizes'] as $size => $size_data) {
                $size_file = $base_dir . '/' . $size_data['file'];

                if (file_exists($size_file)) {
                    $size_file_key = md5($size_file);
                    $stored_size_webp_quality = isset($webp_quality_info[$size_file_key]) ? $webp_quality_info[$size_file_key] : null;
                    $stored_size_avif_quality = isset($avif_quality_info[$size_file_key]) ? $avif_quality_info[$size_file_key] : null;

                    $size_webp_exists = $stored_size_webp_quality !== null;
                    $size_avif_exists = $stored_size_avif_quality !== null;

                    $need_size_webp_regen = !$only_changed_quality ||
                                           ($size_webp_exists && $stored_size_webp_quality !== $current_webp_quality);
                    $need_size_avif_regen = !$only_changed_quality ||
                                           ($size_avif_exists && $stored_size_avif_quality !== $current_avif_quality);

                    if ($need_size_webp_regen && $settings['enable_webp']) {
                        $webp_result = $atlantbh_image_optimizer->convert_to_webp($size_file, true);
                        if ($webp_result) {
                            $stats['webp_success']++;
                        }
                    } else if ($settings['enable_webp']) {
                        $stats['quality_unchanged']++;
                    }

                    if ($need_size_avif_regen && $settings['enable_avif']) {
                        $avif_result = $atlantbh_image_optimizer->convert_to_avif($size_file, true);
                        if ($avif_result) {
                            $stats['avif_success']++;
                        }
                    } else if ($settings['enable_avif']) {
                        $stats['quality_unchanged']++;
                    }
                }
            }
        } catch (Exception $e) {
            $errors[] = 'Error processing image ID ' . $image->ID . ': ' . $e->getMessage();
            $stats['errors']++;
            continue;
        }
    }

    if (!empty($errors)) {
        foreach ($errors as $error) {
            error_log($error);
        }
    }

    return $stats;
}

function image_optimization_page() {
    global $atlantbh_image_optimizer;

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_image_optimization']) && check_admin_referer('save_image_optimization', 'image_optimization_nonce')) {
        $settings = [
            'enable_optimization' => isset($_POST['enable_optimization']) ? 1 : 0,
            'serve_optimized_images' => isset($_POST['serve_optimized_images']) ? 1 : 0,
            'enable_webp' => isset($_POST['enable_webp']) ? 1 : 0,
            'enable_avif' => isset($_POST['enable_avif']) ? 1 : 0,
            'webp_quality' => isset($_POST['webp_quality']) ? intval($_POST['webp_quality']) : 85,
            'avif_quality' => isset($_POST['avif_quality']) ? intval($_POST['avif_quality']) : 75,
        ];

        $settings['webp_quality'] = max(1, min(100, $settings['webp_quality']));
        $settings['avif_quality'] = max(1, min(100, $settings['avif_quality']));

        update_option('atlantbh_image_optimization', $settings);

        echo '<div class="updated"><p>Image optimization settings saved.</p></div>';
    }

    if (isset($_POST['regenerate_webp_avif']) && check_admin_referer('regenerate_webp_avif_nonce')) {
        $only_changed_quality = isset($_POST['only_changed_quality']) ? true : false;
        $stats = atlantbh_regenerate_webp_avif($only_changed_quality);

        echo '<div class="notice notice-success">';
        if ($only_changed_quality) {
            echo '<p>Existing WebP and AVIF images have been selectively updated with new quality settings!</p>';
            echo '<p><strong>Note:</strong> Only existing WebP/AVIF images were processed. Images without WebP/AVIF versions were skipped.</p>';
        } else {
            echo '<p>WebP and AVIF images have been regenerated for all eligible images!</p>';
        }
        echo '<ul>';
        echo '<li>Images processed: ' . $stats['processed'] . '</li>';
        echo '<li>WebP images created/updated: ' . $stats['webp_success'] . '</li>';
        echo '<li>AVIF images created/updated: ' . $stats['avif_success'] . '</li>';
        echo '<li>Images skipped (SVG or other non-convertible formats): ' . $stats['skipped'] . '</li>';
        if ($only_changed_quality) {
            echo '<li>Existing images with unchanged quality settings: ' . $stats['quality_unchanged'] . '</li>';
        }
        echo '<li>Errors encountered: ' . $stats['errors'] . '</li>';
        echo '</ul>';
        echo '<p>Check the PHP error log for details if any errors were encountered.</p>';
        echo '</div>';
    }

    $settings = $atlantbh_image_optimizer->get_settings();
    ?>
    <div class="wrap">
        <h1>Image Optimization Settings</h1>

        <div class="card" style="min-width:100%;">
            <h2>Settings</h2>
            <form method="post">
                <?php wp_nonce_field('save_image_optimization', 'image_optimization_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="enable_optimization">Enable Image Optimization</label>
                        </th>
                        <td>
                            <input type="checkbox" name="enable_optimization" id="enable_optimization" value="1" <?php checked(1, $settings['enable_optimization']); ?> />
                            <p class="description">Enable automatic image optimization for all uploaded images.</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="serve_optimized_images">Serve WebP/AVIF Images</label>
                        </th>
                        <td>
                            <input type="checkbox" name="serve_optimized_images" id="serve_optimized_images" value="1" <?php checked(1, $settings['serve_optimized_images']); ?> />
                            <p class="description">When enabled, WebP/AVIF images will be served to browsers that support them. Disable to compare with original images.</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="enable_webp">Enable WebP Conversion</label>
                        </th>
                        <td>
                            <input type="checkbox" name="enable_webp" id="enable_webp" value="1" <?php checked(1, $settings['enable_webp']); ?> />
                            <p class="description">Convert images to WebP format when possible.</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="webp_quality">WebP Quality (1-100)</label>
                        </th>
                        <td>
                            <input type="number" name="webp_quality" id="webp_quality" value="<?php echo esc_attr($settings['webp_quality']); ?>" min="1" max="100" />
                            <p class="description">Higher values mean better quality but larger file size. Recommended: 80-90.</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="enable_avif">Enable AVIF Conversion</label>
                        </th>
                        <td>
                            <input type="checkbox" name="enable_avif" id="enable_avif" value="1" <?php checked(1, $settings['enable_avif']); ?> />
                            <p class="description">Convert images to AVIF format when possible (requires PHP 8.1+ with GD and AVIF support).</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="avif_quality">AVIF Quality (1-100)</label>
                        </th>
                        <td>
                            <input type="number" name="avif_quality" id="avif_quality" value="<?php echo esc_attr($settings['avif_quality']); ?>" min="1" max="100" />
                            <p class="description">Higher values mean better quality but larger file size. Recommended: 70-80.</p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="save_image_optimization" class="button button-primary" value="Save Changes" />
                </p>
            </form>
        </div>

        <div class="card" style="min-width:100%;margin-top:20px;">
            <h2>Regenerate WebP and AVIF Images</h2>
            <p>This tool will regenerate WebP and AVIF versions for JPG, PNG, and GIF images in your media library.</p>
            <p>This process may take some time depending on the number of images in your library.</p>
            <p><strong>Note:</strong> SVG files will be skipped as they don't need conversion.</p>

            <form method="post">
                <?php wp_nonce_field('regenerate_webp_avif_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="only_changed_quality">Only Update Existing WebP/AVIF Images with Changed Quality</label>
                        </th>
                        <td>
                            <input type="checkbox" name="only_changed_quality" id="only_changed_quality" value="1" />
                            <p class="description">When checked, only <strong>existing</strong> WebP/AVIF images with quality settings different from current settings will be updated. Images without WebP/AVIF versions will be skipped. Leave unchecked to process all images (recommended for first run).</p>
                        </td>
                    </tr>
                </table>

                <p>
                    <input type="submit" name="regenerate_webp_avif" class="button button-primary" value="Regenerate WebP and AVIF Images" />
                </p>
            </form>
        </div>
    </div>
    <?php
}
