<?php
/**
 * Positions Settings Page
 *
 * This file provides a centralized repository for managing position icons used specifically in the
 * Case Study Timeline block. It creates a WordPress admin interface for uploading and organizing
 * position icons that represent different roles, job titles, or organizational positions.
 *
 * RELATED FILES AND INTEGRATION:
 * - Block Editor: resources/scripts/blocks/case-study-timeline.js
 *   Uses window.positionsData.positions to populate the positions repository UI
 * - Frontend Template: resources/views/sections/case-study-timeline.blade.php
 *   Renders position icons and names in the timeline display
 * - REST API: Provides /wp-json/wp/v2/settings/positions endpoint for AJAX fallback
 *
 * DATA STRUCTURE:
 * Each position contains: id (attachment ID), name (display text), url (image URL)
 * Stored in WordPress options table as 'positions' option
 *
 * BLOCK INTEGRATION:
 * The Case Study Timeline block allows users to:
 * 1. Select positions from this central repository (linked by positionId)
 * 2. Add custom positions (positionId = 0) with manual text/icon input
 * 3. Search and filter through available positions
 * 4. Mix repository positions with custom positions in the same timeline
 *
 * The system provides multiple data delivery methods (wp_localize_script, inline scripts,
 * REST API) to ensure reliable access to position data in the block editor environment.
 */

function localize_positions() {
    $positions = get_option('positions', []);

    wp_localize_script('case-study-timeline', 'positionsData', [
        'positions' => $positions
    ]);

    wp_localize_script('case-study-timeline-init', 'positionsData', [
        'positions' => $positions
    ]);

    if (is_admin() && isset($_GET['debug_positions'])) {
        echo '<pre>Positions: ' . esc_html(json_encode($positions, JSON_PRETTY_PRINT)) . '</pre>';
    }

    if (is_admin()) {
        add_action('admin_footer', function() use ($positions) {
            echo '<script type="text/javascript">
            console.log("Positions data from PHP:", ' . json_encode($positions) . ');
            </script>';
        });
    }

    if (is_admin()) {
        add_action('admin_footer', function() use ($positions) {
            echo '<script type="text/javascript">
            window.positionsData = window.positionsData || {};
            window.positionsData.positions = ' . json_encode($positions) . ';
            console.log("Positions injected directly into the page:", window.positionsData.positions);
            </script>';
        });
    }
}
add_action('enqueue_block_editor_assets', 'localize_positions', 99);
add_action('wp_enqueue_scripts', 'localize_positions', 99);

add_action('rest_api_init', function() {
    register_rest_route('wp/v2', '/settings/positions', [
        'methods' => 'GET',
        'callback' => function() {
            return get_option('positions', []);
        },
        'permission_callback' => function() {
            return current_user_can('edit_posts');
        }
    ]);
});

add_action('enqueue_block_editor_assets', function() {
    $positions = get_option('positions', []);

    wp_add_inline_script('wp-blocks', 'window.positionsData = ' . json_encode(['positions' => $positions]) . ';', 'before');
}, 20);

function positions_page() {
    wp_enqueue_media();

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action']) && $_POST['action'] === 'add_position' && check_admin_referer('add_position', 'position_nonce')) {
            if (isset($_POST['icon_id']) && !empty($_POST['icon_id']) && isset($_POST['position_name']) && !empty($_POST['position_name'])) {
                $icon_id = intval($_POST['icon_id']);
                $position_name = sanitize_text_field($_POST['position_name']);

                $positions = get_option('positions', []);

                $positions[] = [
                    'id' => $icon_id,
                    'name' => $position_name,
                    'url' => wp_get_attachment_url($icon_id),
                ];

                update_option('positions', $positions);
                echo '<div class="updated"><p>Position added successfully.</p></div>';
            }
        }

        if (isset($_POST['action']) && $_POST['action'] === 'remove_position' && check_admin_referer('remove_position', 'remove_position_nonce')) {
            if (isset($_POST['position_index']) && is_numeric($_POST['position_index'])) {
                $position_index = intval($_POST['position_index']);
                $positions = get_option('positions', []);

                if (isset($positions[$position_index])) {
                    array_splice($positions, $position_index, 1);
                    update_option('positions', $positions);
                    echo '<div class="updated"><p>Position removed successfully.</p></div>';
                }
            }
        }
    }

    $positions = get_option('positions', []);
    ?>
    <div class="wrap">
        <h1>Positions</h1>
        <p>Upload and manage positions that can be used in the Case Study Timeline block.</p>

        <h2>Add New Position</h2>
        <form method="post" class="add-position-form">
            <?php wp_nonce_field('add_position', 'position_nonce'); ?>
            <input type="hidden" name="action" value="add_position">
            <input type="hidden" name="icon_id" id="position_icon_id" value="">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="position_name">Position Name</label>
                    </th>
                    <td>
                        <input name="position_name" type="text" id="position_name" class="regular-text" required />
                        <p class="description">Enter a descriptive name for this position.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label>Position Icon</label>
                    </th>
                    <td>
                        <div id="position-icon-preview" style="display: none; margin-bottom: 10px;">
                            <img src="" alt="Icon Preview" style="max-width: 100px; max-height: 100px;">
                        </div>
                        <button type="button" class="button" id="upload_position_icon_button">Select Icon</button>
                        <p class="description">Upload or select an image to use as a position icon. Recommended size: 24x24px.</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Add Position', 'primary', 'submit', true, ['id' => 'add_position_submit', 'disabled' => 'disabled']); ?>
        </form>

        <h2>Existing Positions</h2>
        <?php if (empty($positions)) : ?>
            <p>No positions have been added yet.</p>
        <?php else : ?>
            <div class="positions-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach ($positions as $index => $position) : ?>
                    <div class="position-item" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center;">
                        <img src="<?php echo esc_url($position['url']); ?>" alt="<?php echo esc_attr($position['name']); ?>" style="max-width: 80px; max-height: 80px; margin: 0 auto 10px;">
                        <p style="margin: 0; font-weight: bold;"><?php echo esc_html($position['name']); ?></p>
                        <form method="post" style="margin-top: 10px;">
                            <?php wp_nonce_field('remove_position', 'remove_position_nonce'); ?>
                            <input type="hidden" name="action" value="remove_position">
                            <input type="hidden" name="position_index" value="<?php echo $index; ?>">
                            <button type="submit" class="button button-small button-link-delete" onclick="return confirm('Are you sure you want to remove this position?');">Remove</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        $('#upload_position_icon_button').click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            mediaUploader = window.wp.media({
                title: 'Select Position Icon',
                button: {
                    text: 'Use this icon'
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#position_icon_id').val(attachment.id);
                $('#position-icon-preview').show().find('img').attr('src', attachment.url);
                $('#add_position_submit').prop('disabled', false);
            });

            mediaUploader.open();
        });

        $('#position_name').on('input', function() {
            checkFormValidity();
        });

        function checkFormValidity() {
            var iconId = $('#position_icon_id').val();
            var positionName = $('#position_name').val().trim();

            if (iconId && positionName) {
                $('#add_position_submit').prop('disabled', false);
            } else {
                $('#add_position_submit').prop('disabled', true);
            }
        }
    });
    </script>
    <?php
}
