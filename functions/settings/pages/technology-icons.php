<?php
/**
 * Technology Icons Settings Page
 *
 * This file provides a centralized repository for managing technology icons used across multiple
 * Gutenberg blocks. It creates a WordPress admin interface for uploading and organizing technology
 * icons that represent programming languages, frameworks, tools, and other technical assets.
 *
 * RELATED FILES AND INTEGRATION:
 * - Primary Block: resources/scripts/blocks/case-study-technologies.js
 *   Uses window.technologyIconsData.icons for the main technology showcase block
 * - Secondary Block: resources/scripts/blocks/text-image-collection.js
 *   Uses the same icon repository for technology selections in collection items
 * - Frontend Templates:
 *   - resources/views/sections/case-study-technologies.blade.php
 *   - resources/views/sections/text-image-collection.blade.php
 * - Frontend Script: resources/scripts/case-study-technologies-init.js
 *   Handles frontend interactions and debugging for technology blocks
 * - Settings Index: functions/settings/index.php (includes this file)
 * - REST API: Provides /wp-json/wp/v2/settings/technology_icons endpoint
 *
 * DATA STRUCTURE:
 * Each technology icon contains: id (attachment ID), name (display text), url (image URL)
 * Stored in WordPress options table as 'technology_icons' option
 * Recommended icon size: 100x100px for optimal display
 *
 * BLOCK INTEGRATION:
 * - Case Study Technologies: Displays technology icons in organized sections with titles
 * - Text Image Collection: Allows selection of technology icons for individual collection items
 * - Both blocks support search/filter functionality for easy icon selection
 * - Icons are referenced by ID and resolved to current URL/name data during frontend rendering
 *
 * The system provides multiple data delivery methods (wp_localize_script, inline scripts,
 * REST API) to ensure reliable access to technology icon data in various block editor scenarios.
 */

function localize_technology_icons() {
    $tech_icons = get_option('technology_icons', []);

    wp_localize_script('case-study-technologies', 'technologyIconsData', [
        'icons' => $tech_icons
    ]);

    wp_localize_script('case-study-technologies-init', 'technologyIconsData', [
        'icons' => $tech_icons
    ]);

    if (is_admin() && isset($_GET['debug_tech_icons'])) {
        echo '<pre>Technology Icons: ' . esc_html(json_encode($tech_icons, JSON_PRETTY_PRINT)) . '</pre>';
    }

    if (is_admin()) {
        add_action('admin_footer', function() use ($tech_icons) {
            echo '<script type="text/javascript">
            window.technologyIconsData = window.technologyIconsData || {};
            window.technologyIconsData.icons = ' . json_encode($tech_icons) . ';
            console.log("Technology icons injected directly into the page:", window.technologyIconsData.icons);
            </script>';
        });
    }
}
add_action('enqueue_block_editor_assets', 'localize_technology_icons', 99);
add_action('wp_enqueue_scripts', 'localize_technology_icons', 99);

add_action('rest_api_init', function() {
    register_rest_route('wp/v2', '/settings/technology_icons', [
        'methods' => 'GET',
        'callback' => function() {
            return get_option('technology_icons', []);
        },
        'permission_callback' => function() {
            return current_user_can('edit_posts');
        }
    ]);
});

add_action('enqueue_block_editor_assets', function() {
    $tech_icons = get_option('technology_icons', []);

    wp_add_inline_script('wp-blocks', 'window.technologyIconsData = ' . json_encode(['icons' => $tech_icons]) . ';', 'before');
}, 20);

function technology_icons_page() {
    wp_enqueue_media();

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action']) && $_POST['action'] === 'add_icon' && check_admin_referer('add_technology_icon', 'technology_icon_nonce')) {
            if (isset($_POST['icon_id']) && !empty($_POST['icon_id']) && isset($_POST['icon_name']) && !empty($_POST['icon_name'])) {
                $icon_id = intval($_POST['icon_id']);
                $icon_name = sanitize_text_field($_POST['icon_name']);

                $tech_icons = get_option('technology_icons', []);

                $tech_icons[] = [
                    'id' => $icon_id,
                    'name' => $icon_name,
                    'url' => wp_get_attachment_url($icon_id),
                ];

                update_option('technology_icons', $tech_icons);
                echo '<div class="updated"><p>Technology icon added successfully.</p></div>';
            }
        }

        if (isset($_POST['action']) && $_POST['action'] === 'remove_icon' && check_admin_referer('remove_technology_icon', 'remove_icon_nonce')) {
            if (isset($_POST['icon_index']) && is_numeric($_POST['icon_index'])) {
                $icon_index = intval($_POST['icon_index']);
                $tech_icons = get_option('technology_icons', []);

                if (isset($tech_icons[$icon_index])) {
                    array_splice($tech_icons, $icon_index, 1);
                    update_option('technology_icons', $tech_icons);
                    echo '<div class="updated"><p>Technology icon removed successfully.</p></div>';
                }
            }
        }
    }

    $tech_icons = get_option('technology_icons', []);
    ?>
    <div class="wrap">
        <h1>Technology Icons</h1>
        <p>Upload and manage technology icons that can be used in the Case Study Technologies block.</p>

        <h2>Add New Technology Icon</h2>
        <form method="post" class="add-technology-icon-form">
            <?php wp_nonce_field('add_technology_icon', 'technology_icon_nonce'); ?>
            <input type="hidden" name="action" value="add_icon">
            <input type="hidden" name="icon_id" id="technology_icon_id" value="">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="icon_name">Icon Name</label>
                    </th>
                    <td>
                        <input name="icon_name" type="text" id="icon_name" class="regular-text" required />
                        <p class="description">Enter a descriptive name for this technology icon.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label>Icon Image</label>
                    </th>
                    <td>
                        <div id="technology-icon-preview" style="display: none; margin-bottom: 10px;">
                            <img src="" alt="Icon Preview" style="max-width: 100px; max-height: 100px;">
                        </div>
                        <button type="button" class="button" id="upload_technology_icon_button">Select Icon</button>
                        <p class="description">Upload or select an image to use as a technology icon. Recommended size: 100x100px.</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Add Technology Icon', 'primary', 'submit', true, ['id' => 'add_technology_icon_submit', 'disabled' => 'disabled']); ?>
        </form>

        <h2>Existing Technology Icons</h2>
        <?php if (empty($tech_icons)) : ?>
            <p>No technology icons have been added yet.</p>
        <?php else : ?>
            <div class="technology-icons-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach ($tech_icons as $index => $icon) : ?>
                    <div class="technology-icon-item" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center;">
                        <img src="<?php echo esc_url($icon['url']); ?>" alt="<?php echo esc_attr($icon['name']); ?>" style="max-width: 80px; max-height: 80px; margin: 0 auto 10px;">
                        <p style="margin: 0; font-weight: bold;"><?php echo esc_html($icon['name']); ?></p>
                        <form method="post" style="margin-top: 10px;">
                            <?php wp_nonce_field('remove_technology_icon', 'remove_icon_nonce'); ?>
                            <input type="hidden" name="action" value="remove_icon">
                            <input type="hidden" name="icon_index" value="<?php echo $index; ?>">
                            <button type="submit" class="button button-small button-link-delete" onclick="return confirm('Are you sure you want to remove this icon?');">Remove</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        $('#upload_technology_icon_button').click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            mediaUploader = window.wp.media({
                title: 'Select Technology Icon',
                button: {
                    text: 'Use this icon'
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#technology_icon_id').val(attachment.id);
                $('#technology-icon-preview').show().find('img').attr('src', attachment.url);
                $('#add_technology_icon_submit').prop('disabled', false);
            });

            mediaUploader.open();
        });

        $('#icon_name').on('input', function() {
            checkFormValidity();
        });

        function checkFormValidity() {
            var iconId = $('#technology_icon_id').val();
            var iconName = $('#icon_name').val().trim();

            if (iconId && iconName) {
                $('#add_technology_icon_submit').prop('disabled', false);
            } else {
                $('#add_technology_icon_submit').prop('disabled', true);
            }
        }
    });
    </script>
    <?php
}
