<?php
/**
 * Contact Forms Settings Page
 *
 * This file provides the WordPress admin interface for managing contact form configurations
 * and default submission settings. It serves as the central control panel for the theme's
 * contact form system, which uses Gutenberg blocks for form creation and REST API for submissions.
 *
 * RELATED FILES AND INTEGRATION:
 * - Admin Menu: functions/settings/admin-menu.php (creates "Contact Forms" submenu)
 * - CPT Registration: functions/settings/cpt-registration.php (registers contact_form post type)
 * - Block Editor: resources/scripts/blocks/contact-form.js (Gutenberg contact form block)
 * - Frontend Template: resources/views/sections/contact-form.blade.php (renders contact forms)
 * - Frontend Scripts: resources/scripts/contact-form-init.js (handles form interactions, validation, Swiper)
 * - Form Processing: functions/form.php (REST API endpoints for form submission and file sending)
 * - Blog Integration: resources/views/partials/content-single.blade.php (displays forms on blog posts)
 * - Settings Entry: functions/settings/index.php (includes this file)
 *
 * SYSTEM ARCHITECTURE:
 * 1. Contact Form Creation: Administrators create contact forms using Gutenberg blocks
 * 2. Form Storage: Forms are saved as contact_form post type with block content
 * 3. Form Assignment: Blog posts reference forms via 'blog_post_contact_form_id' option
 * 4. Form Display: Blog posts parse and render contact form blocks automatically
 * 5. Form Submission: Frontend JavaScript submits to /wp-json/sage/send-contact-form
 * 6. Email Processing: Submissions are emailed to configurable addresses with SMTP
 *
 * CONFIGURATION OPTIONS:
 * - blog_post_contact_form_id: WordPress option storing the ID of the contact form post
 * - contact_form_email: Default email address for form submissions (fallback)
 * - Individual forms can override submission email via block attributes
 *
 * FORM FEATURES:
 * - Two form types: 'slider' (with image carousel) and 'static' (single image)
 * - Category selection with dynamic pill interface
 * - Form validation using JustValidate library
 * - Success/error overlays with auto-close functionality
 * - SMTP email delivery with AWS SES configuration
 * - File attachment support via separate CTA endpoint
 *
 * The system provides a complete contact form solution from creation to submission processing,
 * with centralized management through this settings page.
 */

function contact_forms_page() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST'
        && isset($_POST['default_contact_email'])
        && check_admin_referer('save_contact_email', 'contact_email_nonce')) {
         update_option('contact_form_email', sanitize_email($_POST['default_contact_email']));
         echo '<div class="updated"><p>Default contact email saved.</p></div>';
    }
    $default_email = get_option('contact_form_email', '');
    ?>
    <div class="wrap">
        <h1>Contact Forms</h1>
        <h2>Blog Post Contact Form</h2>
        <p>Edit the blog post contact form using the Gutenberg editor:</p>
        <p>
            <a href="<?php echo esc_url(get_edit_post_link(get_option('blog_post_contact_form_id'))); ?>" class="button button-primary">
                Edit Contact Form
            </a>
        </p>
        <h3>Default Submission Email</h3>
        <form method="post">
            <?php wp_nonce_field('save_contact_email', 'contact_email_nonce'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="default_contact_email">Default Submission Email</label>
                    </th>
                    <td>
                        <input name="default_contact_email" type="email" id="default_contact_email" value="<?php echo esc_attr($default_email); ?>" class="regular-text" />
                    </td>
                </tr>
            </table>
            <?php submit_button('Save Email'); ?>
        </form>
    </div>
    <?php
}
