<?php
function contact_forms_page() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST'
        && isset($_POST['default_contact_email'])
        && check_admin_referer('save_contact_email', 'contact_email_nonce')) {
         update_option('contact_form_email', sanitize_email($_POST['default_contact_email']));
         echo '<div class="updated"><p>Default contact email saved.</p></div>';
    }
    $default_email = get_option('contact_form_email', '');
    ?>
    <div class="wrap">
        <h1>Contact Forms</h1>
        <h2>Blog Post Contact Form</h2>
        <p>Edit the blog post contact form using the Gutenberg editor:</p>
        <p>
            <a href="<?php echo esc_url(get_edit_post_link(get_option('blog_post_contact_form_id'))); ?>" class="button button-primary">
                Edit Contact Form
            </a>
        </p>
        <h3>Default Submission Email</h3>
        <form method="post">
            <?php wp_nonce_field('save_contact_email', 'contact_email_nonce'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="default_contact_email">Default Submission Email</label>
                    </th>
                    <td>
                        <input name="default_contact_email" type="email" id="default_contact_email" value="<?php echo esc_attr($default_email); ?>" class="regular-text" />
                    </td>
                </tr>
            </table>
            <?php submit_button('Save Email'); ?>
        </form>
    </div>
    <?php
}
