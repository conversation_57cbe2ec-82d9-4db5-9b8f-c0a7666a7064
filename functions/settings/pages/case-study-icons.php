<?php

function localize_case_study_icons() {
    $case_study_icons = get_option('case_study_icons', []);

    $case_study_blocks = [
        'case-study-features',
        'case-study-technologies',
        'case-study-text-image',
        'case-study-timeline',
        'case-study-outcomes'
    ];

    foreach ($case_study_blocks as $block) {
        wp_localize_script($block, 'caseStudyIconsData', [
            'icons' => $case_study_icons
        ]);
    }

    if (is_admin() && isset($_GET['debug_case_study_icons'])) {
        echo '<pre>Case Study Icons: ' . esc_html(json_encode($case_study_icons, JSON_PRETTY_PRINT)) . '</pre>';
    }
}

add_action('enqueue_block_editor_assets', 'localize_case_study_icons', 99);
add_action('wp_enqueue_scripts', 'localize_case_study_icons', 99);

add_action('rest_api_init', function() {
    register_rest_route('wp/v2', '/settings/case_study_icons', [
        'methods' => 'GET',
        'callback' => function() {
            return get_option('case_study_icons', []);
        },
        'permission_callback' => function() {
            return current_user_can('edit_posts');
        }
    ]);
});

add_action('enqueue_block_editor_assets', function() {
    $case_study_icons = get_option('case_study_icons', []);

    wp_add_inline_script('wp-blocks', 'window.caseStudyIconsData = ' . json_encode(['icons' => $case_study_icons]) . ';', 'before');
}, 20);

function case_study_icons_page() {
    wp_enqueue_media();

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action']) && $_POST['action'] === 'add_icon' && check_admin_referer('add_case_study_icon', 'case_study_icon_nonce')) {
            if (isset($_POST['icon_id']) && !empty($_POST['icon_id']) && isset($_POST['icon_name']) && !empty($_POST['icon_name']) && isset($_POST['icon_color']) && !empty($_POST['icon_color'])) {
                $icon_id = intval($_POST['icon_id']);
                $icon_name = sanitize_text_field($_POST['icon_name']);
                $icon_color = sanitize_text_field($_POST['icon_color']);

                $case_study_icons = get_option('case_study_icons', []);

                $case_study_icons[] = [
                    'id' => $icon_id,
                    'name' => $icon_name,
                    'url' => wp_get_attachment_url($icon_id),
                    'color' => $icon_color,
                ];

                update_option('case_study_icons', $case_study_icons);
                echo '<div class="updated"><p>Case study icon added successfully.</p></div>';
            }
        }

        if (isset($_POST['action']) && $_POST['action'] === 'remove_icon' && check_admin_referer('remove_case_study_icon', 'remove_icon_nonce')) {
            if (isset($_POST['icon_index']) && is_numeric($_POST['icon_index'])) {
                $icon_index = intval($_POST['icon_index']);
                $case_study_icons = get_option('case_study_icons', []);

                if (isset($case_study_icons[$icon_index])) {
                    array_splice($case_study_icons, $icon_index, 1);
                    update_option('case_study_icons', $case_study_icons);
                    echo '<div class="updated"><p>Case study icon removed successfully.</p></div>';
                }
            }
        }
    }

    $case_study_icons = get_option('case_study_icons', []);
    ?>
    <div class="wrap">
        <h1>Case Study Icons</h1>
        <p>Upload and manage icons that can be used in all Case Study blocks.</p>

        <h2>Add New Case Study Icon</h2>
        <form method="post" class="add-case-study-icon-form">
            <?php wp_nonce_field('add_case_study_icon', 'case_study_icon_nonce'); ?>
            <input type="hidden" name="action" value="add_icon">
            <input type="hidden" name="icon_id" id="case_study_icon_id" value="">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="icon_name">Icon Name</label>
                    </th>
                    <td>
                        <input name="icon_name" type="text" id="icon_name" class="regular-text" required />
                        <p class="description">Enter a descriptive name for this icon.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="icon_color">Icon Color</label>
                    </th>
                    <td>
                        <input name="icon_color" type="color" id="icon_color" value="#538564" required />
                        <p class="description">Choose a color for this icon. This will be used as the accent color in case study blocks.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label>Icon Image</label>
                    </th>
                    <td>
                        <div id="case-study-icon-preview" style="display: none; margin-bottom: 10px;">
                            <img src="" alt="Icon Preview" style="max-width: 100px; max-height: 100px;">
                        </div>
                        <button type="button" class="button" id="upload_case_study_icon_button">Select Icon</button>
                        <p class="description">Upload or select an image to use as an icon. Recommended size: 24x24px.</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Add Icon', 'primary', 'add_case_study_icon_submit', true, ['disabled' => 'disabled', 'id' => 'add_case_study_icon_submit']); ?>
        </form>

        <h2>Existing Case Study Icons</h2>
        <?php if (empty($case_study_icons)) : ?>
            <p>No case study icons have been added yet.</p>
        <?php else : ?>
            <div class="case-study-icons-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach ($case_study_icons as $index => $icon) : ?>
                    <div class="case-study-icon-item" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center;">
                        <div style="width: 42px; height: 42px; border-radius: 50%; background-color: <?php echo esc_attr($icon['color']); ?>; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                            <img src="<?php echo esc_url($icon['url']); ?>" alt="<?php echo esc_attr($icon['name']); ?>" style="max-width: 24px; max-height: 24px;">
                        </div>
                        <p style="margin: 0; font-weight: bold;"><?php echo esc_html($icon['name']); ?></p>
                        <p style="margin: 5px 0 10px; color: #666;"><?php echo esc_html($icon['color']); ?></p>
                        <form method="post" style="margin-top: 10px;">
                            <?php wp_nonce_field('remove_case_study_icon', 'remove_icon_nonce'); ?>
                            <input type="hidden" name="action" value="remove_icon">
                            <input type="hidden" name="icon_index" value="<?php echo $index; ?>">
                            <button type="submit" class="button button-small button-link-delete" onclick="return confirm('Are you sure you want to remove this icon?');">Remove</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        $('#upload_case_study_icon_button').click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            mediaUploader = window.wp.media({
                title: 'Select Case Study Icon',
                button: {
                    text: 'Use this icon'
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#case_study_icon_id').val(attachment.id);
                $('#case-study-icon-preview').show().find('img').attr('src', attachment.url);
                $('#add_case_study_icon_submit').prop('disabled', false);
            });

            mediaUploader.open();
        });
    });
    </script>
    <?php
}
