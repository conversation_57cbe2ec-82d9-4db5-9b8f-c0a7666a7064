<?php
/**
 * Custom Post Type Registration
 *
 * This file registers custom post types that support the theme's content management system.
 * Currently handles the contact_form post type which enables Gutenberg-based contact form
 * creation and management through the theme settings interface.
 *
 * RELATED FILES AND INTEGRATION:
 * - Entry Point: functions/settings/index.php (includes this file)
 * - Admin Interface: functions/settings/admin-menu.php (creates "Contact Forms" submenu)
 * - Settings Page: functions/settings/pages/contact-forms.php (manages contact form settings)
 * - Form Processing: functions/form.php (handles form submissions via REST API)
 * - Frontend Display: resources/views/sections/contact-form.blade.php (renders contact forms)
 * - Frontend Scripts: resources/scripts/contact-form-init.js (handles form interactions)
 * - Blog Integration: resources/views/partials/content-single.blade.php (displays forms on blog posts)
 *
 * CONTACT FORM POST TYPE:
 * - Purpose: Stores contact form configurations created with Gutenberg blocks
 * - Visibility: Hidden from public (show_ui=true for admin, public=false for frontend)
 * - Menu: Hidden from main admin menu (managed through Theme Settings instead)
 * - Editor: Full Gutenberg support with title and editor fields
 * - REST API: Enabled for block editor functionality
 * - Usage: Blog posts reference contact forms by ID stored in 'blog_post_contact_form_id' option
 *
 * The contact form system allows administrators to create reusable contact forms using
 * Gutenberg blocks, then assign them to blog posts or other content areas. Form submissions
 * are processed through the REST API and emailed to configurable addresses.
 */

function register_contact_form_cpt() {
    $args = [
        'public'       => false,
        'show_ui'      => true,
        'show_in_menu' => false,
        'supports'     => ['title', 'editor'],
        'show_in_rest' => true,
    ];
    register_post_type('contact_form', $args);
}
add_action('init', 'register_contact_form_cpt');
