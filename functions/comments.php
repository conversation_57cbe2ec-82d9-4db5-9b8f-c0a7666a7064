<?php
/**
 * Comment System Disabling
 *
 * Completely disables WordPress comment functionality across the site.
 * Removes comment support, closes comments, hides admin menu, and redirects comment pages.
 *
 * RELATED FILES:
 * - resources/views/partials/comments.blade.php (comment template - unused due to this)
 * - app/View/Composers/Comments.php (comment data composer - unused due to this)
 * - resources/views/partials/content-single.blade.php (calls comments_template() but shows nothing)
 */

function disable_comments_post_types_support() {
    $post_types = ['post', 'page'];
    foreach ($post_types as $post_type) {
        remove_post_type_support($post_type, 'comments');
        remove_post_type_support($post_type, 'trackbacks');
    }
}
add_action('init', 'disable_comments_post_types_support');

function disable_comments_status() {
    return false;
}
add_filter('comments_open', 'disable_comments_status', 20, 2);
add_filter('pings_open', 'disable_comments_status', 20, 2);

function disable_comments_admin_menu() {
    remove_menu_page('edit-comments.php');
}
add_action('admin_menu', 'disable_comments_admin_menu');

function disable_comments_redirect() {
    global $pagenow;
    if ($pagenow === 'edit-comments.php') {
        wp_redirect(admin_url());
        exit;
    }
}
add_action('admin_init', 'disable_comments_redirect');
