<?php
/**
 * Image Optimization System
 *
 * Automatically converts JPG, PNG, and GIF images to WebP and AVIF formats on upload.
 * Serves optimized images based on browser support with fallback to originals.
 * Includes quality settings, browser detection, and bulk regeneration capabilities.
 *
 * RELATED FILES:
 * - functions/settings/pages/image-optimization.php (admin settings interface)
 * - functions/settings/admin-menu.php (admin menu integration)
 *
 * FEATURES:
 * - Automatic conversion on upload and thumbnail generation
 * - Browser support detection via Accept headers and User-Agent
 * - Quality tracking to avoid unnecessary regeneration
 * - Content filtering to replace image URLs in post content
 * - Bulk regeneration tools for existing media library
 */

if (!defined('ABSPATH')) {
    exit;
}

class AtlantBH_Image_Optimizer {
    public function __construct() {
        add_filter('mime_types', [$this, 'add_webp_avif_mime_types']);
        add_filter('wp_handle_upload', [$this, 'convert_uploaded_image'], 10, 2);
        add_filter('image_make_intermediate_size', [$this, 'convert_intermediate_image']);
        add_filter('wp_get_attachment_image_src', [$this, 'serve_optimized_image'], 10, 4);
        add_filter('wp_calculate_image_srcset', [$this, 'serve_optimized_srcset'], 10, 5);
        add_filter('the_content', [$this, 'replace_image_urls_in_content']);
    }

    public function add_webp_avif_mime_types($mime_types) {
        $mime_types['webp'] = 'image/webp';
        $mime_types['avif'] = 'image/avif';
        return $mime_types;
    }

    public function convert_uploaded_image($file, $context) {
        if ($context !== 'upload') {
            return $file;
        }

        if (!$this->is_convertible_image($file['file'])) {
            return $file;
        }

        $settings = $this->get_settings();

        if (!$settings['enable_optimization']) {
            return $file;
        }

        if ($settings['enable_webp']) {
            $this->convert_to_webp($file['file']);
        }

        if ($settings['enable_avif']) {
            $this->convert_to_avif($file['file']);
        }

        return $file;
    }

    public function convert_intermediate_image($file) {
        $settings = $this->get_settings();

        if (!$settings['enable_optimization']) {
            return $file;
        }

        if (!$this->is_convertible_image($file)) {
            return $file;
        }

        if ($settings['enable_webp']) {
            $this->convert_to_webp($file);
        }

        if ($settings['enable_avif']) {
            $this->convert_to_avif($file);
        }

        return $file;
    }

    public function serve_optimized_image($image, $attachment_id, $size, $icon) {
        if (!$image) {
            return $image;
        }

        $settings = $this->get_settings();

        if (!$settings['enable_optimization'] || !$settings['serve_optimized_images']) {
            return $image;
        }

        $supports_webp = $this->browser_supports_webp();
        $supports_avif = $this->browser_supports_avif();

        $image_url = $image[0];
        $image_path = $this->get_file_path_from_url($image_url);

        if (!$image_path) {
            return $image;
        }

        if ($supports_avif && $settings['enable_avif']) {
            $avif_path = $this->get_avif_path($image_path);
            if (file_exists($avif_path)) {
                $image[0] = $this->get_url_from_file_path($avif_path);
                return $image;
            }
        }

        if ($supports_webp && $settings['enable_webp']) {
            $webp_path = $this->get_webp_path($image_path);
            if (file_exists($webp_path)) {
                $image[0] = $this->get_url_from_file_path($webp_path);
                return $image;
            }
        }

        return $image;
    }

    public function serve_optimized_srcset($sources, $size_array, $image_src, $image_meta, $attachment_id) {
        if (empty($sources)) {
            return $sources;
        }

        $settings = $this->get_settings();

        if (!$settings['enable_optimization'] || !$settings['serve_optimized_images']) {
            return $sources;
        }

        $supports_webp = $this->browser_supports_webp();
        $supports_avif = $this->browser_supports_avif();

        if (!$supports_webp && !$supports_avif) {
            return $sources;
        }

        foreach ($sources as &$source) {
            $image_url = $source['url'];
            $image_path = $this->get_file_path_from_url($image_url);

            if (!$image_path) {
                continue;
            }

            if ($supports_avif && $settings['enable_avif']) {
                $avif_path = $this->get_avif_path($image_path);
                if (file_exists($avif_path)) {
                    $source['url'] = $this->get_url_from_file_path($avif_path);
                    continue;
                }
            }

            if ($supports_webp && $settings['enable_webp']) {
                $webp_path = $this->get_webp_path($image_path);
                if (file_exists($webp_path)) {
                    $source['url'] = $this->get_url_from_file_path($webp_path);
                }
            }
        }

        return $sources;
    }

    public function replace_image_urls_in_content($content) {
        if (empty($content) || !is_string($content)) {
            return $content;
        }

        $settings = $this->get_settings();

        if (!$settings['enable_optimization'] || !$settings['serve_optimized_images']) {
            return $content;
        }

        $supports_webp = $this->browser_supports_webp();
        $supports_avif = $this->browser_supports_avif();

        if (!$supports_webp && !$supports_avif) {
            return $content;
        }

        if (!function_exists('mb_convert_encoding')) {
            return $content;
        }

        $dom = new DOMDocument();

        @$dom->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'));

        $images = $dom->getElementsByTagName('img');

        if ($images->length === 0) {
            return $content;
        }

        $modified = false;
        foreach ($images as $img) {
            $src = $img->getAttribute('src');
            $image_path = $this->get_file_path_from_url($src);

            if (!$image_path) {
                continue;
            }

            if ($supports_avif && $settings['enable_avif']) {
                $avif_path = $this->get_avif_path($image_path);
                if (file_exists($avif_path)) {
                    $img->setAttribute('src', $this->get_url_from_file_path($avif_path));
                    $modified = true;
                    continue;
                }
            }

            if ($supports_webp && $settings['enable_webp']) {
                $webp_path = $this->get_webp_path($image_path);
                if (file_exists($webp_path)) {
                    $img->setAttribute('src', $this->get_url_from_file_path($webp_path));
                    $modified = true;
                }
            }
        }

        if (!$modified) {
            return $content;
        }

        $body = $dom->getElementsByTagName('body')->item(0);
        $new_content = '';
        foreach ($body->childNodes as $child) {
            $new_content .= $dom->saveHTML($child);
        }

        return $new_content;
    }

    public function browser_supports_webp() {
        if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false) {
            return true;
        }

        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $ua = $_SERVER['HTTP_USER_AGENT'];

            if (
                (strpos($ua, 'Chrome/') !== false && preg_match('/Chrome\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 32) ||
                (strpos($ua, 'Edg/') !== false && preg_match('/Edg\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 18) ||
                (strpos($ua, 'Firefox/') !== false && preg_match('/Firefox\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 65) ||
                (strpos($ua, 'OPR/') !== false && preg_match('/OPR\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 19) ||
                (strpos($ua, 'Safari/') !== false && strpos($ua, 'Version/') !== false && preg_match('/Version\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 14)
            ) {
                return true;
            }
        }

        return false;
    }

    public function browser_supports_avif() {
        if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/avif') !== false) {
            return true;
        }

        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $ua = $_SERVER['HTTP_USER_AGENT'];

            if (
                (strpos($ua, 'Chrome/') !== false && preg_match('/Chrome\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 85) ||
                (strpos($ua, 'Firefox/') !== false && preg_match('/Firefox\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 86) ||
                (strpos($ua, 'Safari/') !== false && strpos($ua, 'Version/') !== false && preg_match('/Version\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 16)
            ) {
                return true;
            }
        }

        return false;
    }

    public function is_convertible_image($file_path) {
        $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        return in_array($ext, ['jpg', 'jpeg', 'png', 'gif']);
    }

    public function convert_to_webp($file_path, $force_regenerate = false) {
        try {
            $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

            if (in_array($ext, ['webp', 'avif', 'svg'])) {
                return false;
            }

            $webp_path = $this->get_webp_path($file_path);

            $settings = $this->get_settings();
            $current_quality = $settings['webp_quality'];

            $quality_info = get_option('atlantbh_webp_quality_info', []);
            $file_key = md5($file_path);
            $stored_quality = isset($quality_info[$file_key]) ? $quality_info[$file_key] : null;

            if (!$force_regenerate &&
                file_exists($webp_path) &&
                filemtime($webp_path) >= filemtime($file_path) &&
                $stored_quality === $current_quality) {
                return true;
            }

            if (!function_exists('imagewebp')) {
                return false;
            }

            switch ($ext) {
                case 'jpg':
                case 'jpeg':
                    $image = imagecreatefromjpeg($file_path);
                    break;
                case 'png':
                    $image = imagecreatefrompng($file_path);
                    if ($image) {
                        imagealphablending($image, false);
                        imagesavealpha($image, true);
                    }
                    break;
                case 'gif':
                    $image = imagecreatefromgif($file_path);
                    break;
                default:
                    return false;
            }

            if (!$image) {
                return false;
            }

            if (function_exists('imagepalettetotruecolor') && !imagepalettetotruecolor($image)) {
                $width = imagesx($image);
                $height = imagesy($image);

                $truecolor = imagecreatetruecolor($width, $height);

                imagealphablending($truecolor, false);
                imagesavealpha($truecolor, true);
                $transparent = imagecolorallocatealpha($truecolor, 255, 255, 255, 127);
                imagefilledrectangle($truecolor, 0, 0, $width, $height, $transparent);

                imagecopyresampled($truecolor, $image, 0, 0, 0, 0, $width, $height, $width, $height);

                imagedestroy($image);

                $image = $truecolor;
            }

            $settings = $this->get_settings();
            $quality = $settings['webp_quality'];

            $temp_file = $webp_path . '.tmp';

            $result = imagewebp($image, $temp_file, $quality);

            imagedestroy($image);

            if ($result && file_exists($temp_file)) {
                if (file_exists($webp_path)) {
                    unlink($webp_path);
                }
                rename($temp_file, $webp_path);

                $quality_info = get_option('atlantbh_webp_quality_info', []);
                $file_key = md5($file_path);
                $quality_info[$file_key] = $quality;
                update_option('atlantbh_webp_quality_info', $quality_info);

                return true;
            } else if (file_exists($temp_file)) {
                unlink($temp_file);
            }

            return false;
        } catch (Exception $e) {
            error_log('Error converting to WebP: ' . $e->getMessage() . ' - File: ' . $file_path);
            return false;
        }
    }

    public function convert_to_avif($file_path, $force_regenerate = false) {
        try {
            $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

            if (in_array($ext, ['avif', 'svg'])) {
                return false;
            }

            $avif_path = $this->get_avif_path($file_path);

            $settings = $this->get_settings();
            $current_quality = $settings['avif_quality'];

            $quality_info = get_option('atlantbh_avif_quality_info', []);
            $file_key = md5($file_path);
            $stored_quality = isset($quality_info[$file_key]) ? $quality_info[$file_key] : null;

            if (!$force_regenerate &&
                file_exists($avif_path) &&
                filemtime($avif_path) >= filemtime($file_path) &&
                $stored_quality === $current_quality) {
                return true;
            }

            if (!function_exists('imageavif')) {
                return false;
            }

            switch ($ext) {
                case 'jpg':
                case 'jpeg':
                    $image = imagecreatefromjpeg($file_path);
                    break;
                case 'png':
                    $image = imagecreatefrompng($file_path);
                    if ($image) {
                        imagealphablending($image, false);
                        imagesavealpha($image, true);
                    }
                    break;
                case 'gif':
                    $image = imagecreatefromgif($file_path);
                    break;
                default:
                    return false;
            }

            if (!$image) {
                return false;
            }

            if (function_exists('imagepalettetotruecolor') && !imagepalettetotruecolor($image)) {
                $width = imagesx($image);
                $height = imagesy($image);

                $truecolor = imagecreatetruecolor($width, $height);

                imagealphablending($truecolor, false);
                imagesavealpha($truecolor, true);
                $transparent = imagecolorallocatealpha($truecolor, 255, 255, 255, 127);
                imagefilledrectangle($truecolor, 0, 0, $width, $height, $transparent);

                imagecopyresampled($truecolor, $image, 0, 0, 0, 0, $width, $height, $width, $height);

                imagedestroy($image);

                $image = $truecolor;
            }

            $settings = $this->get_settings();
            $quality = $settings['avif_quality'];

            $temp_file = $avif_path . '.tmp';

            $result = imageavif($image, $temp_file, $quality);

            imagedestroy($image);

            if ($result && file_exists($temp_file)) {
                if (file_exists($avif_path)) {
                    unlink($avif_path);
                }
                rename($temp_file, $avif_path);

                $quality_info = get_option('atlantbh_avif_quality_info', []);
                $file_key = md5($file_path);
                $quality_info[$file_key] = $quality;
                update_option('atlantbh_avif_quality_info', $quality_info);

                return true;
            } else if (file_exists($temp_file)) {
                unlink($temp_file);
            }

            return false;
        } catch (Exception $e) {
            error_log('Error converting to AVIF: ' . $e->getMessage() . ' - File: ' . $file_path);
            return false;
        }
    }

    public function load_image($file_path) {
        $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        switch ($ext) {
            case 'jpg':
            case 'jpeg':
                $image = imagecreatefromjpeg($file_path);
                break;
            case 'png':
                $image = imagecreatefrompng($file_path);
                if ($image) {
                    imagealphablending($image, false);
                    imagesavealpha($image, true);
                }
                break;
            default:
                return false;
        }

        return $image;
    }

    public function get_webp_path($file_path) {
        return preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $file_path);
    }

    public function get_avif_path($file_path) {
        return preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.avif', $file_path);
    }

    public function get_file_path_from_url($url) {
        $upload_dir = wp_upload_dir();

        $url = set_url_scheme($url);

        if (strpos($url, $upload_dir['baseurl']) === false) {
            return false;
        }

        $path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);

        if (!file_exists($path)) {
            return false;
        }

        return $path;
    }

    public function get_url_from_file_path($file_path) {
        $upload_dir = wp_upload_dir();

        if (strpos($file_path, $upload_dir['basedir']) === false) {
            return false;
        }

        $url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $file_path);

        return $url;
    }


    public function get_default_settings() {
        return [
            'enable_optimization' => 1,
            'serve_optimized_images' => 1,
            'enable_webp' => 1,
            'webp_quality' => 85,
            'enable_avif' => 1,
            'avif_quality' => 75,
        ];
    }

    public function get_settings() {
        $defaults = $this->get_default_settings();
        $saved_settings = get_option('atlantbh_image_optimization', $defaults);

        $settings = wp_parse_args($saved_settings, $defaults);

        return $settings;
    }
}

$atlantbh_image_optimizer = new AtlantBH_Image_Optimizer();
